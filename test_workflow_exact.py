#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test exact du workflow de l'application SOMACA
"""

import os
import sys
import time
import tempfile
import shutil

def test_exact_workflow():
    """Test exact du workflow de l'application"""
    print("=== TEST WORKFLOW EXACT SOMACA ===")
    
    # Créer un dossier temporaire pour les tests
    temp_dir = tempfile.mkdtemp()
    print(f"Dossier temporaire: {temp_dir}")
    
    try:
        # Étape 1: Créer un fichier Excel d'entrée (comme l'utilisateur)
        print("\n1. Création fichier Excel d'entrée...")
        import pandas as pd
        
        input_data = pd.DataFrame({
            'Nom': ['Produit1', 'Produit2', 'Produit3'],
            'Description': ['Desc1', 'Desc2', 'Desc3']
        })
        
        input_file = os.path.join(temp_dir, "input.xlsx")
        input_data.to_excel(input_file, index=False)
        print(f"✅ Fichier d'entrée créé: {input_file}")
        
        # Étape 2: Simuler la génération de codes (comme l'app)
        print("\n2. Simulation génération codes...")
        import openpyxl
        from openpyxl.drawing.image import Image as OpenpyxlImage
        from PIL import Image, ImageDraw
        import io
        
        # Lire le fichier d'entrée
        df = pd.read_excel(input_file)
        print(f"✅ Fichier lu: {len(df)} lignes")
        
        # Créer un nouveau workbook pour les codes
        output_file = os.path.join(temp_dir, "output_with_codes.xlsx")
        wb = openpyxl.Workbook()
        ws = wb.active
        
        # Ajouter les données
        for i, row in df.iterrows():
            ws.cell(row=i+1, column=1, value=row['Nom'])
            ws.cell(row=i+1, column=2, value=row['Description'])
            
            # Créer une image simple (simuler code-barres)
            img = Image.new('RGB', (100, 30), 'white')
            draw = ImageDraw.Draw(img)
            draw.rectangle([10, 10, 90, 20], fill='black')
            
            # Sauvegarder en mémoire
            img_buffer = io.BytesIO()
            img.save(img_buffer, format='PNG')
            img_buffer.seek(0)
            
            # Ajouter à Excel
            excel_img = OpenpyxlImage(img_buffer)
            excel_img.anchor = f'C{i+1}'
            ws.add_image(excel_img)
        
        # Sauvegarder le fichier avec codes
        wb.save(output_file)
        wb.close()
        print(f"✅ Fichier avec codes créé: {output_file}")
        
        # Étape 3: Simuler la lecture pour impression (PROBLÈME POTENTIEL)
        print("\n3. Simulation lecture pour impression...")
        
        # Lire le fichier généré (comme les fonctions d'impression)
        wb_read = openpyxl.load_workbook(output_file)
        ws_read = wb_read.active
        
        print(f"✅ Fichier lu pour impression: {ws_read.max_row} lignes")
        print(f"✅ Images trouvées: {len(ws_read._images) if hasattr(ws_read, '_images') else 0}")
        
        # TESTER: Fermer explicitement
        wb_read.close()
        print("✅ Workbook fermé après lecture")
        
        # Étape 4: Simuler nouvelle sélection de fichier
        print("\n4. Simulation nouvelle sélection...")
        
        # Créer un nouveau fichier d'entrée
        input_data2 = pd.DataFrame({
            'Nom': ['NouveauProduit1', 'NouveauProduit2'],
            'Description': ['NouvelleDesc1', 'NouvelleDesc2']
        })
        
        input_file2 = os.path.join(temp_dir, "input2.xlsx")
        input_data2.to_excel(input_file2, index=False)
        print(f"✅ Nouveau fichier créé: {input_file2}")
        
        # Lire le nouveau fichier
        df2 = pd.read_excel(input_file2)
        print(f"✅ Nouveau fichier lu: {len(df2)} lignes")
        
        # Étape 5: Test de suppression des fichiers
        print("\n5. Test suppression fichiers...")
        
        try:
            os.remove(output_file)
            print("✅ Fichier output supprimé")
        except Exception as e:
            print(f"❌ PROBLÈME: Impossible de supprimer output_file: {e}")
        
        try:
            os.remove(input_file)
            print("✅ Fichier input supprimé")
        except Exception as e:
            print(f"❌ PROBLÈME: Impossible de supprimer input_file: {e}")
        
        try:
            os.remove(input_file2)
            print("✅ Fichier input2 supprimé")
        except Exception as e:
            print(f"❌ PROBLÈME: Impossible de supprimer input_file2: {e}")
        
        print("\n✅ WORKFLOW COMPLET RÉUSSI")
        
    except Exception as e:
        print(f"❌ ERREUR WORKFLOW: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Nettoyer le dossier temporaire
        try:
            shutil.rmtree(temp_dir)
            print(f"✅ Dossier temporaire nettoyé: {temp_dir}")
        except Exception as e:
            print(f"⚠️ Attention nettoyage: {e}")
    
    print("\n=== FIN TEST WORKFLOW ===")

def test_specific_app_functions():
    """Test spécifique des fonctions de l'app qui posent problème"""
    print("\n=== TEST FONCTIONS SPÉCIFIQUES APP ===")
    
    # Importer les modules de l'app
    try:
        # Simuler l'import des fonctions de l'app
        print("Test des imports de l'app...")
        
        # Test: Vérifier si le problème vient des imports
        import openpyxl
        import pandas as pd
        from PIL import Image
        import qrcode
        from barcode import Code128
        from barcode.writer import ImageWriter
        
        print("✅ Tous les imports OK")
        
        # Test: Créer et fermer plusieurs workbooks rapidement
        print("\nTest création/fermeture multiple workbooks...")
        for i in range(3):
            wb = openpyxl.Workbook()
            ws = wb.active
            ws['A1'] = f"Test {i}"
            
            # Sauvegarder temporairement
            temp_file = f"temp_test_{i}.xlsx"
            wb.save(temp_file)
            wb.close()
            
            # Relire immédiatement
            wb2 = openpyxl.load_workbook(temp_file)
            value = wb2.active['A1'].value
            wb2.close()
            
            # Supprimer
            os.remove(temp_file)
            print(f"✅ Cycle {i+1} OK: {value}")
        
        print("✅ Test cycles multiples OK")
        
    except Exception as e:
        print(f"❌ Erreur test spécifique: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_exact_workflow()
    test_specific_app_functions()
