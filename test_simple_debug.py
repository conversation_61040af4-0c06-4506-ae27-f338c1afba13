#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test simple pour identifier le problème sans importer l'app complète
"""

import os
import sys
import time
import tempfile
import shutil

def test_simple_probleme():
    """Test simple du problème de fichiers verrouillés"""
    print("=== TEST SIMPLE PROBLÈME ===")
    
    temp_dir = tempfile.mkdtemp()
    print(f"Dossier temporaire: {temp_dir}")
    
    try:
        import pandas as pd
        import openpyxl
        from PIL import Image, ImageDraw
        import io
        
        # Étape 1: Créer un fichier Excel avec des images (comme l'app)
        print("\n1. Création fichier Excel avec images...")
        
        # Données de base
        data = pd.DataFrame({
            'Nom': ['Produit1', 'Produit2'],
            'Description': ['Desc1', 'Desc2']
        })
        
        excel_file = os.path.join(temp_dir, "test_with_images.xlsx")
        
        # Créer le workbook
        wb = openpyxl.Workbook()
        ws = wb.active
        
        # Ajouter les données
        for i, row in data.iterrows():
            ws.cell(row=i+2, column=1, value=row['Nom'])
            ws.cell(row=i+2, column=2, value=row['Description'])
            
            # Créer une image simple
            img = Image.new('RGB', (100, 30), 'white')
            draw = ImageDraw.Draw(img)
            draw.rectangle([10, 10, 90, 20], fill='black')
            
            # Convertir en bytes
            img_buffer = io.BytesIO()
            img.save(img_buffer, format='PNG')
            img_buffer.seek(0)
            
            # Ajouter à Excel
            from openpyxl.drawing.image import Image as OpenpyxlImage
            excel_img = OpenpyxlImage(img_buffer)
            excel_img.anchor = f'C{i+2}'
            ws.add_image(excel_img)
        
        # Sauvegarder
        wb.save(excel_file)
        wb.close()
        print(f"✅ Fichier Excel créé: {excel_file}")
        
        # Étape 2: Lire le fichier SANS fermer (simuler le bug)
        print("\n2. Lecture fichier SANS fermeture...")
        wb_read = openpyxl.load_workbook(excel_file)
        ws_read = wb_read.active
        
        print(f"✅ Fichier lu: {ws_read.max_row} lignes")
        print(f"✅ Images: {len(ws_read._images) if hasattr(ws_read, '_images') else 0}")
        
        # NE PAS FERMER LE WORKBOOK (simuler le bug)
        # wb_read.close()  # <-- COMMENTÉ POUR SIMULER LE BUG
        
        # Étape 3: Essayer de supprimer le fichier
        print("\n3. Test suppression avec fichier ouvert...")
        try:
            os.remove(excel_file)
            print("✅ Suppression OK (pas de verrouillage)")
        except PermissionError as e:
            print(f"❌ VERROUILLAGE CONFIRMÉ: {e}")
            
            # Fermer maintenant et réessayer
            print("   Fermeture forcée du workbook...")
            wb_read.close()
            time.sleep(0.5)
            
            try:
                os.remove(excel_file)
                print("✅ Suppression OK après fermeture")
            except Exception as e2:
                print(f"❌ Toujours verrouillé: {e2}")
        
        # Étape 4: Test avec fermeture correcte
        print("\n4. Test avec fermeture correcte...")
        
        # Recréer le fichier
        wb2 = openpyxl.Workbook()
        ws2 = wb2.active
        ws2['A1'] = "Test fermeture"
        wb2.save(excel_file)
        wb2.close()
        
        # Lire et fermer correctement
        wb_read2 = openpyxl.load_workbook(excel_file)
        value = wb_read2.active['A1'].value
        wb_read2.close()  # FERMETURE CORRECTE
        
        # Essayer de supprimer
        try:
            os.remove(excel_file)
            print("✅ Suppression OK avec fermeture correcte")
        except Exception as e:
            print(f"❌ Erreur même avec fermeture: {e}")
        
        # Étape 5: Test cycles multiples
        print("\n5. Test cycles multiples...")
        
        for i in range(3):
            # Créer
            test_file = os.path.join(temp_dir, f"cycle_{i}.xlsx")
            wb = openpyxl.Workbook()
            wb.active['A1'] = f"Cycle {i}"
            wb.save(test_file)
            wb.close()
            
            # Lire
            wb_read = openpyxl.load_workbook(test_file)
            value = wb_read.active['A1'].value
            wb_read.close()
            
            # Supprimer
            os.remove(test_file)
            print(f"✅ Cycle {i+1} OK: {value}")
        
        print("\n✅ TOUS LES TESTS TERMINÉS")
        
    except Exception as e:
        print(f"❌ ERREUR: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Nettoyer
        try:
            shutil.rmtree(temp_dir)
            print(f"✅ Nettoyage: {temp_dir}")
        except Exception as e:
            print(f"⚠️ Erreur nettoyage: {e}")

def test_variables_globales():
    """Test pour voir si le problème vient des variables globales"""
    print("\n=== TEST VARIABLES GLOBALES ===")
    
    # Simuler les variables globales de l'app
    class FakeGenerator:
        def __init__(self):
            self.input_file = None
            self.output_folder = None
            self.last_generated_file = None
            self._barcode_cache = {}
            self._qr_cache = {}
            self.is_cancelled = False
    
    # Créer une instance globale (comme dans l'app)
    global_generator = FakeGenerator()
    
    print("✅ Instance globale créée")
    
    # Test 1: Première utilisation
    global_generator.input_file = "fichier1.xlsx"
    global_generator.last_generated_file = "output1.xlsx"
    global_generator._barcode_cache["test"] = "cache1"
    
    print(f"✅ Première config: {global_generator.input_file}")
    print(f"   Cache: {len(global_generator._barcode_cache)} items")
    
    # Test 2: Changement de fichier (comme le problème)
    global_generator.input_file = "fichier2.xlsx"
    # Le cache et last_generated_file restent de l'ancienne session
    
    print(f"✅ Nouvelle config: {global_generator.input_file}")
    print(f"   Ancien last_generated: {global_generator.last_generated_file}")
    print(f"   Cache non nettoyé: {len(global_generator._barcode_cache)} items")
    
    # Test 3: Nettoyage manuel
    global_generator.last_generated_file = None
    global_generator._barcode_cache.clear()
    global_generator._qr_cache.clear()
    
    print("✅ Nettoyage manuel effectué")
    print(f"   Cache après nettoyage: {len(global_generator._barcode_cache)} items")

if __name__ == "__main__":
    test_simple_probleme()
    test_variables_globales()
