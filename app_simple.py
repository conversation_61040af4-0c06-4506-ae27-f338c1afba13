#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Version simplifiée pour tester l'adaptation de fenêtre
"""

import webview
import os
import sys

class SimpleApi:
    def __init__(self):
        pass
    
    def test_function(self):
        return {"success": True, "message": "Test réussi"}

def get_optimal_window_size():
    """Calculer la taille optimale de la fenêtre selon l'écran"""
    try:
        # Essayer d'obtenir les dimensions de l'écran
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()  # Cacher la fenêtre tkinter
        screen_width = root.winfo_screenwidth()
        screen_height = root.winfo_screenheight()
        root.destroy()
        
        # Calculer des dimensions optimales (75% de l'écran avec des limites)
        optimal_width = min(max(int(screen_width * 0.75), 1000), 1400)
        optimal_height = min(max(int(screen_height * 0.75), 700), 900)
        
        print(f"Écran détecté: {screen_width}x{screen_height}")
        print(f"Fenêtre optimale: {optimal_width}x{optimal_height}")
        
        return optimal_width, optimal_height
        
    except Exception as e:
        print(f"Impossible de détecter l'écran: {e}")
        # Valeurs par défaut sécurisées
        return 1200, 800

def main():
    """Fonction principale"""
    print("Démarrage de l'application simple...")
    
    # Chemin vers le dossier web
    web_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'web')
    
    # Créer l'API
    api = SimpleApi()

    # Obtenir la taille optimale de la fenêtre
    optimal_width, optimal_height = get_optimal_window_size()

    print(f"Création de la fenêtre {optimal_width}x{optimal_height}...")

    # Créer la fenêtre native avec adaptation automatique
    window = webview.create_window(
        'Test - Générateur QR & Code à Barre - SOMACA',
        url=os.path.join(web_dir, 'index.html'),
        js_api=api,
        width=optimal_width,
        height=optimal_height,
        min_width=900,           # Largeur minimale pour éviter les problèmes d'affichage
        min_height=600,          # Hauteur minimale pour éviter les problèmes d'affichage
        resizable=True,          # Permettre le redimensionnement
        maximized=False,         # Ne pas démarrer en plein écran
        on_top=False,           # Ne pas rester au-dessus des autres fenêtres
        shadow=True,            # Ombre de fenêtre pour un meilleur aspect
        vibrancy=False          # Désactiver les effets de transparence
    )
    
    print("Démarrage de l'interface...")
    # Démarrer l'application
    webview.start(debug=True)

if __name__ == '__main__':
    main()
