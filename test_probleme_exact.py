#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test pour reproduire exactement le problème décrit par l'utilisateur
"""

import os
import sys
import time

# Ajouter le répertoire courant au path pour importer l'app
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_probleme_utilisateur():
    """
    Test exact du problème:
    1. Sélectionner fichier et générer codes -> OK
    2. Utiliser impression (tickets/images) -> OK  
    3. Sélectionner nouveau fichier -> PROBLÈME
    """
    print("=== TEST PROBLÈME UTILISATEUR ===")
    
    try:
        # Importer la classe de l'app
        from app_native import SomacaBarcodeGenerator
        
        print("✅ Import SomacaBarcodeGenerator OK")
        
        # Créer une instance comme dans l'app
        generator = SomacaBarcodeGenerator()
        print("✅ Instance créée")
        
        # Étape 1: Créer un fichier Excel de test
        print("\n1. Création fichier Excel de test...")
        import pandas as pd
        
        test_data = pd.DataFrame({
            'Nom': ['TestProduit1', 'TestProduit2'],
            'Description': ['TestDesc1', 'TestDesc2']
        })
        
        input_file = "test_input.xlsx"
        output_folder = "test_output"
        
        # Créer le dossier de sortie
        os.makedirs(output_folder, exist_ok=True)
        
        # Sauvegarder le fichier d'entrée
        test_data.to_excel(input_file, index=False)
        print(f"✅ Fichier test créé: {input_file}")
        
        # Étape 2: Configurer l'instance
        print("\n2. Configuration instance...")
        generator.input_file = input_file
        generator.output_folder = output_folder
        print("✅ Configuration OK")
        
        # Étape 3: Générer les codes (première fois)
        print("\n3. Génération codes (première fois)...")
        result = generator.generate_codes()
        
        if result.get("success"):
            print("✅ Génération codes OK")
            print(f"   Fichier généré: {generator.last_generated_file}")
        else:
            print(f"❌ Erreur génération: {result.get('message')}")
            return
        
        # Étape 4: Simuler l'utilisation des fonctions d'impression
        print("\n4. Test fonctions d'impression...")
        
        # Test fonction generate_renault_etiquettes (celle qui pose problème)
        print("   Test generate_renault_etiquettes...")
        result_etiquettes = generator.generate_renault_etiquettes()
        
        if result_etiquettes.get("success"):
            print("✅ generate_renault_etiquettes OK")
        else:
            print(f"⚠️ generate_renault_etiquettes: {result_etiquettes.get('message')}")
        
        # Étape 5: Essayer de sélectionner un nouveau fichier
        print("\n5. Test nouvelle sélection fichier...")
        
        # Créer un nouveau fichier
        test_data2 = pd.DataFrame({
            'Nom': ['NouveauProduit1', 'NouveauProduit2'],
            'Description': ['NouvelleDesc1', 'NouvelleDesc2']
        })
        
        input_file2 = "test_input2.xlsx"
        test_data2.to_excel(input_file2, index=False)
        print(f"✅ Nouveau fichier créé: {input_file2}")
        
        # Essayer de changer le fichier d'entrée
        print("   Changement fichier d'entrée...")
        old_file = generator.input_file
        generator.input_file = input_file2
        print(f"✅ Fichier changé: {old_file} -> {input_file2}")
        
        # Étape 6: Essayer de générer à nouveau
        print("\n6. Test génération avec nouveau fichier...")
        result2 = generator.generate_codes()
        
        if result2.get("success"):
            print("✅ Deuxième génération OK - PROBLÈME RÉSOLU!")
            print(f"   Nouveau fichier généré: {generator.last_generated_file}")
        else:
            print(f"❌ PROBLÈME CONFIRMÉ: {result2.get('message')}")
            
            # Diagnostic détaillé
            print("\n🔍 DIAGNOSTIC DÉTAILLÉ:")
            print(f"   - Fichier d'entrée: {generator.input_file}")
            print(f"   - Existe: {os.path.exists(generator.input_file)}")
            print(f"   - Dossier sortie: {generator.output_folder}")
            print(f"   - Existe: {os.path.exists(generator.output_folder)}")
            print(f"   - Last generated: {getattr(generator, 'last_generated_file', 'None')}")
            
            # Vérifier les attributs de l'instance
            print(f"   - is_cancelled: {getattr(generator, 'is_cancelled', 'None')}")
            print(f"   - progress: {getattr(generator, 'progress', 'None')}")
            
            # Vérifier les caches
            cache_info = []
            if hasattr(generator, '_barcode_cache'):
                cache_info.append(f"barcode_cache: {len(generator._barcode_cache)} items")
            if hasattr(generator, '_qr_cache'):
                cache_info.append(f"qr_cache: {len(generator._qr_cache)} items")
            if hasattr(generator, '_style_cache'):
                cache_info.append(f"style_cache: {len(generator._style_cache)} items")
            
            print(f"   - Caches: {', '.join(cache_info) if cache_info else 'Aucun'}")
        
    except Exception as e:
        print(f"❌ ERREUR TEST: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Nettoyer les fichiers de test
        print("\n🧹 Nettoyage...")
        for file in ["test_input.xlsx", "test_input2.xlsx"]:
            try:
                if os.path.exists(file):
                    os.remove(file)
                    print(f"✅ {file} supprimé")
            except Exception as e:
                print(f"⚠️ Erreur suppression {file}: {e}")
        
        # Nettoyer le dossier de sortie
        try:
            import shutil
            if os.path.exists("test_output"):
                shutil.rmtree("test_output")
                print("✅ Dossier test_output supprimé")
        except Exception as e:
            print(f"⚠️ Erreur suppression dossier: {e}")
    
    print("\n=== FIN TEST PROBLÈME ===")

if __name__ == "__main__":
    test_probleme_utilisateur()
