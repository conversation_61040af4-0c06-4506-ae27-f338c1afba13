#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test simple pour vérifier l'application
"""

import sys
import os

try:
    print("Test 1: Import des modules de base...")
    import webview
    print("✓ webview importé")
    
    print("Test 2: Vérification du dossier web...")
    web_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'web')
    index_path = os.path.join(web_dir, 'index.html')
    
    if os.path.exists(web_dir):
        print(f"✓ Dossier web trouvé: {web_dir}")
    else:
        print(f"✗ Dossier web non trouvé: {web_dir}")
        
    if os.path.exists(index_path):
        print(f"✓ Fichier index.html trouvé: {index_path}")
    else:
        print(f"✗ Fichier index.html non trouvé: {index_path}")
    
    print("Test 3: Import de l'application...")
    from app_native import SomacaBarcodeGenerator, Api, get_optimal_window_size
    print("✓ Classes importées")
    
    print("Test 4: Test de la fonction de taille optimale...")
    width, height = get_optimal_window_size()
    print(f"✓ Taille optimale calculée: {width}x{height}")
    
    print("Test 5: Création des objets...")
    generator = SomacaBarcodeGenerator()
    api = Api()
    print("✓ Objets créés")
    
    print("\n🎉 Tous les tests sont passés ! L'application devrait fonctionner.")
    print(f"Vous pouvez maintenant lancer: python app_native.py")
    
except Exception as e:
    print(f"❌ Erreur lors du test: {e}")
    import traceback
    traceback.print_exc()
