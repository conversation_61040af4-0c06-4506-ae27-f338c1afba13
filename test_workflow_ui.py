#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test du workflow exact de l'interface utilisateur
"""

import os
import sys
import tempfile
import shutil

def test_workflow_ui():
    """Test exact du workflow UI"""
    print("=== TEST WORKFLOW UI ===")
    
    temp_dir = tempfile.mkdtemp()
    print(f"Dossier temporaire: {temp_dir}")
    
    try:
        import pandas as pd
        
        # Créer deux fichiers Excel de test
        print("\n1. Création fichiers de test...")
        
        # Fichier 1
        data1 = pd.DataFrame({
            'Nom': ['Produit1', 'Produit2'],
            'Description': ['Desc1', 'Desc2']
        })
        file1 = os.path.join(temp_dir, "fichier1.xlsx")
        data1.to_excel(file1, index=False)
        
        # Fichier 2
        data2 = pd.DataFrame({
            'Nom': ['NouveauProduit1', 'NouveauProduit2'],
            'Description': ['NouvelleDesc1', 'NouvelleDesc2']
        })
        file2 = os.path.join(temp_dir, "fichier2.xlsx")
        data2.to_excel(file2, index=False)
        
        print(f"✅ Fichier 1: {file1}")
        print(f"✅ Fichier 2: {file2}")
        
        # Simuler la classe Api de l'app
        class FakeApi:
            def __init__(self):
                self.generator = None
                self.setup_generator()
            
            def setup_generator(self):
                """Simuler la création du générateur"""
                self.generator = FakeGenerator()
                print("✅ Générateur créé")
            
            def set_input_file(self, file_path):
                """Simuler set_input_file"""
                print(f"\n📁 set_input_file appelé: {file_path}")
                
                # Vérifications comme dans l'app
                if not file_path:
                    return {"success": False, "message": "Aucun fichier sélectionné"}
                
                if not os.path.exists(file_path):
                    return {"success": False, "message": "Fichier non trouvé"}
                
                # Vérifier l'extension
                if not file_path.lower().endswith(('.xlsx', '.xls')):
                    return {"success": False, "message": "Format non supporté"}
                
                # Configurer le générateur
                self.generator.input_file = file_path
                
                # Définir le dossier de sortie
                output_folder = os.path.join(os.path.dirname(file_path), "output")
                os.makedirs(output_folder, exist_ok=True)
                self.generator.output_folder = output_folder
                
                print(f"✅ Fichier configuré: {file_path}")
                print(f"✅ Dossier sortie: {output_folder}")
                
                return {"success": True, "message": "Fichier sélectionné"}
            
            def generate_codes(self):
                """Simuler generate_codes"""
                print(f"\n🔧 generate_codes appelé")
                
                if not self.generator.input_file:
                    return {"success": False, "message": "Aucun fichier sélectionné"}
                
                if not os.path.exists(self.generator.input_file):
                    return {"success": False, "message": "Fichier d'entrée non trouvé"}
                
                # Simuler la génération
                output_file = os.path.join(
                    self.generator.output_folder,
                    f"generated_{os.path.basename(self.generator.input_file)}"
                )
                
                # Lire le fichier d'entrée
                df = pd.read_excel(self.generator.input_file)
                print(f"✅ Fichier lu: {len(df)} lignes")
                
                # Créer le fichier de sortie (simulé)
                df.to_excel(output_file, index=False)
                
                # Stocker le fichier généré
                self.generator.last_generated_file = output_file
                
                print(f"✅ Fichier généré: {output_file}")
                
                return {
                    "success": True,
                    "message": f"Codes générés avec succès",
                    "output_file": output_file
                }
            
            def generate_renault_etiquettes(self):
                """Simuler la fonction d'impression qui pose problème"""
                print(f"\n🎫 generate_renault_etiquettes appelé")
                
                # Vérifier qu'un fichier généré existe
                if not hasattr(self.generator, 'last_generated_file') or not self.generator.last_generated_file:
                    return {"success": False, "message": "Aucun fichier Excel généré trouvé"}
                
                if not os.path.exists(self.generator.last_generated_file):
                    return {"success": False, "message": "Fichier Excel généré non trouvé"}
                
                print(f"✅ Fichier trouvé: {self.generator.last_generated_file}")
                
                # Simuler la lecture du fichier (comme dans l'app)
                import openpyxl
                wb = openpyxl.load_workbook(self.generator.last_generated_file)
                ws = wb.active
                
                print(f"✅ Fichier lu pour impression: {ws.max_row} lignes")
                
                # FERMER LE WORKBOOK (correction appliquée)
                wb.close()
                print("✅ Workbook fermé")
                
                return {"success": True, "action": "show_choice_modal"}
        
        class FakeGenerator:
            def __init__(self):
                self.input_file = None
                self.output_folder = None
                self.last_generated_file = None
                self._barcode_cache = {}
                self._qr_cache = {}
                self.is_cancelled = False
                self.progress = 0
        
        # Test du workflow complet
        print("\n2. Test workflow complet...")
        
        api = FakeApi()
        
        # Étape 1: Sélectionner le premier fichier
        print("\n--- PREMIÈRE SÉLECTION ---")
        result1 = api.set_input_file(file1)
        print(f"Résultat: {result1}")
        
        # Générer les codes
        result_gen1 = api.generate_codes()
        print(f"Génération: {result_gen1}")
        
        # Utiliser la fonction d'impression
        result_print1 = api.generate_renault_etiquettes()
        print(f"Impression: {result_print1}")
        
        # Étape 2: Sélectionner le deuxième fichier (LE PROBLÈME)
        print("\n--- DEUXIÈME SÉLECTION (PROBLÈME) ---")
        result2 = api.set_input_file(file2)
        print(f"Résultat: {result2}")
        
        # Vérifier l'état du générateur
        print(f"\n🔍 État du générateur:")
        print(f"   input_file: {api.generator.input_file}")
        print(f"   output_folder: {api.generator.output_folder}")
        print(f"   last_generated_file: {api.generator.last_generated_file}")
        print(f"   Cache barcode: {len(api.generator._barcode_cache)} items")
        print(f"   is_cancelled: {api.generator.is_cancelled}")
        
        # Essayer de générer avec le nouveau fichier
        result_gen2 = api.generate_codes()
        print(f"\nGénération 2: {result_gen2}")
        
        if result_gen2["success"]:
            print("✅ PROBLÈME RÉSOLU - Deuxième génération OK")
        else:
            print("❌ PROBLÈME CONFIRMÉ - Deuxième génération échoue")
            print(f"   Erreur: {result_gen2['message']}")
        
    except Exception as e:
        print(f"❌ ERREUR: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Nettoyer
        try:
            shutil.rmtree(temp_dir)
            print(f"\n✅ Nettoyage: {temp_dir}")
        except Exception as e:
            print(f"⚠️ Erreur nettoyage: {e}")

if __name__ == "__main__":
    test_workflow_ui()
