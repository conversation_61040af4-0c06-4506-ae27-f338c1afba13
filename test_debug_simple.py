#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test simple pour identifier exactement où se produit le blocage
"""

import os
import sys
import time

def test_file_operations():
    """Test simple des opérations de fichier"""
    print("=== TEST DEBUG SIMPLE ===")
    
    # Test 1: Vérifier si on peut créer et supprimer des fichiers
    print("\n1. Test création/suppression fichier...")
    test_file = "test_temp.txt"
    try:
        with open(test_file, 'w') as f:
            f.write("test")
        print("✅ Création fichier OK")
        
        os.remove(test_file)
        print("✅ Suppression fichier OK")
    except Exception as e:
        print(f"❌ Erreur fichier: {e}")
    
    # Test 2: Vérifier openpyxl
    print("\n2. Test openpyxl...")
    try:
        import openpyxl
        print("✅ Import openpyxl OK")
        
        # Créer un fichier Excel simple
        wb = openpyxl.Workbook()
        ws = wb.active
        ws['A1'] = "Test"
        test_excel = "test_excel.xlsx"
        wb.save(test_excel)
        wb.close()
        print("✅ Création Excel OK")
        
        # Lire le fichier Excel
        wb2 = openpyxl.load_workbook(test_excel)
        value = wb2.active['A1'].value
        wb2.close()
        print(f"✅ Lecture Excel OK: {value}")
        
        # Supprimer le fichier
        os.remove(test_excel)
        print("✅ Suppression Excel OK")
        
    except Exception as e:
        print(f"❌ Erreur openpyxl: {e}")
    
    # Test 3: Vérifier pandas
    print("\n3. Test pandas...")
    try:
        import pandas as pd
        print("✅ Import pandas OK")
        
        # Créer un DataFrame simple
        df = pd.DataFrame({'A': [1, 2], 'B': [3, 4]})
        test_excel_pd = "test_pandas.xlsx"
        df.to_excel(test_excel_pd, index=False)
        print("✅ Création Excel pandas OK")
        
        # Lire avec pandas
        df2 = pd.read_excel(test_excel_pd)
        print(f"✅ Lecture Excel pandas OK: {len(df2)} lignes")
        
        # Supprimer
        os.remove(test_excel_pd)
        print("✅ Suppression Excel pandas OK")
        
    except Exception as e:
        print(f"❌ Erreur pandas: {e}")
    
    # Test 4: Simuler le problème de l'app
    print("\n4. Test simulation problème app...")
    try:
        import openpyxl
        import pandas as pd
        
        # Créer un fichier comme l'app
        df = pd.DataFrame({
            'Colonne1': ['Test1', 'Test2'],
            'Colonne2': ['Desc1', 'Desc2']
        })
        
        test_file = "simulation_app.xlsx"
        df.to_excel(test_file, index=False)
        print("✅ Fichier créé avec pandas")
        
        # Lire avec openpyxl comme l'app
        wb = openpyxl.load_workbook(test_file)
        ws = wb.active
        print(f"✅ Fichier lu avec openpyxl: {ws.max_row} lignes")
        
        # FERMER EXPLICITEMENT
        wb.close()
        print("✅ Workbook fermé explicitement")
        
        # Attendre un peu
        time.sleep(0.5)
        
        # Essayer de supprimer
        os.remove(test_file)
        print("✅ Fichier supprimé après fermeture")
        
        # Test: Ouvrir SANS fermer (comme le bug)
        df.to_excel(test_file, index=False)
        wb = openpyxl.load_workbook(test_file)
        ws = wb.active
        print("✅ Fichier ouvert SANS fermeture...")
        
        # Essayer de supprimer SANS fermer
        try:
            os.remove(test_file)
            print("✅ Suppression OK même sans fermeture")
        except Exception as e:
            print(f"❌ PROBLÈME TROUVÉ: {e}")
            # Fermer maintenant
            wb.close()
            time.sleep(0.5)
            os.remove(test_file)
            print("✅ Suppression OK après fermeture forcée")
        
    except Exception as e:
        print(f"❌ Erreur simulation: {e}")
    
    print("\n=== FIN TEST DEBUG ===")

if __name__ == "__main__":
    test_file_operations()
