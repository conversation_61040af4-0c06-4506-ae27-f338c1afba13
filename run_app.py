#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de lancement pour l'application SOMACA
"""

import sys
import os

# Ajouter le répertoire courant au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    print("Lancement de l'application SOMACA...")
    
    # Import de l'application
    from app_native import main
    
    print("Application importée avec succès, démarrage...")
    
    # Lancer l'application
    main()
    
except Exception as e:
    print(f"Erreur lors du lancement: {e}")
    import traceback
    traceback.print_exc()
    input("Appuyez sur Entrée pour fermer...")
